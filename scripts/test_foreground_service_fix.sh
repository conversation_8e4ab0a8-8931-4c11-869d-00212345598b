#!/bin/bash

# Foreground Service Fix - Automated ADB Testing Script
# Tests UnifiedBatteryNotificationService fix for Android 12+ background restrictions

set -e

# Configuration
PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
SERVICE_NAME="$PACKAGE_NAME/.features.stats.notifications.UnifiedBatteryNotificationService"
MAIN_ACTIVITY="$PACKAGE_NAME/.activity.main.MainActivity"
LOG_FILE="foreground_service_test_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if device is connected
check_device() {
    log "Checking ADB device connection..."
    if ! adb devices | grep -q "device$"; then
        log_error "No ADB device connected. Please connect a device or start an emulator."
        exit 1
    fi
    
    local device_info=$(adb shell getprop ro.build.version.release)
    log "Connected to Android $device_info"
    
    local api_level=$(adb shell getprop ro.build.version.sdk)
    if [ "$api_level" -lt 31 ]; then
        log_warning "Device API level is $api_level. Android 12+ (API 31+) recommended for testing background restrictions."
    else
        log_success "Device API level $api_level is suitable for testing Android 12+ restrictions"
    fi
}

# Install app
install_app() {
    log "Installing app..."
    local apk_path="app/build/outputs/apk/debug/app-debug.apk"
    
    if [ ! -f "$apk_path" ]; then
        log_error "APK not found at $apk_path. Please build the app first."
        exit 1
    fi
    
    if adb install -r "$apk_path" 2>&1 | tee -a "$LOG_FILE"; then
        log_success "App installed successfully"
    else
        log_error "Failed to install app"
        exit 1
    fi
}

# Monitor logs for specific patterns
monitor_logs() {
    local pattern="$1"
    local duration="$2"
    local description="$3"
    
    log "Monitoring logs for $description (${duration}s)..."
    timeout "$duration" adb logcat | grep -E "$pattern" | tee -a "$LOG_FILE" &
    local monitor_pid=$!
    sleep "$duration"
    kill $monitor_pid 2>/dev/null || true
}

# Test 1: Background App Startup
test_background_startup() {
    log "=== Test 1: Background App Startup ==="
    
    # Force stop app
    adb shell am force-stop "$PACKAGE_NAME"
    sleep 2
    
    # Clear logcat
    adb logcat -c
    
    # Start service from background context
    log "Starting service from background context..."
    adb shell am start-service "$SERVICE_NAME"
    
    # Monitor for error handling
    monitor_logs "(UnifiedBatteryService|ServiceManager|ForegroundServiceStartNotAllowedException|ERROR)" 10 "background startup error handling"
    
    # Check if service is running
    if adb shell dumpsys activity services | grep -q "UnifiedBatteryNotificationService"; then
        log_success "Service started successfully in background context"
    else
        log_warning "Service not detected in running services (may be in fallback mode)"
    fi
}

# Test 2: Foreground Context Startup
test_foreground_startup() {
    log "=== Test 2: Foreground Context Startup ==="
    
    # Force stop app
    adb shell am force-stop "$PACKAGE_NAME"
    sleep 2
    
    # Clear logcat
    adb logcat -c
    
    # Start app normally
    log "Starting app in foreground..."
    adb shell am start -n "$MAIN_ACTIVITY"
    sleep 5
    
    # Monitor service startup
    monitor_logs "(ServiceManager_Foreground|UnifiedBatteryService_Foreground|UnifiedBatteryService_Lifecycle)" 10 "foreground service startup"
    
    # Verify services are running
    if adb shell dumpsys activity services | grep -q "UnifiedBatteryNotificationService"; then
        log_success "Foreground service started successfully"
    else
        log_error "Foreground service failed to start"
    fi
}

# Test 3: Service Restart Scenarios
test_service_restart() {
    log "=== Test 3: Service Restart Scenarios ==="
    
    # Start app normally first
    adb shell am start -n "$MAIN_ACTIVITY"
    sleep 5
    
    # Kill services
    log "Killing services..."
    adb shell am force-stop "$PACKAGE_NAME"
    
    # Put app in background
    adb shell input keyevent KEYCODE_HOME
    sleep 2
    
    # Clear logcat
    adb logcat -c
    
    # Try to restart service
    log "Attempting service restart from background..."
    adb shell am start-service "$SERVICE_NAME"
    
    # Monitor restart behavior
    monitor_logs "(ServiceManager|UnifiedBatteryService_Fallback|ERROR)" 10 "service restart handling"
    
    log_success "Service restart test completed"
}

# Test 4: Battery Monitoring Functionality
test_battery_monitoring() {
    log "=== Test 4: Battery Monitoring Functionality ==="
    
    # Start service
    adb shell am start-service "$SERVICE_NAME"
    sleep 3
    
    # Clear logcat
    adb logcat -c
    
    # Simulate charging state changes
    log "Simulating charging state changes..."
    adb shell dumpsys battery set ac 1
    sleep 5
    adb shell dumpsys battery set ac 0
    sleep 5
    
    # Monitor battery monitoring
    monitor_logs "(UnifiedBatteryService_Monitoring|CoreBatteryStatsService|BatteryHistoryManager)" 10 "battery monitoring"
    
    # Reset battery state
    adb shell dumpsys battery reset
    
    log_success "Battery monitoring test completed"
}

# Test 5: Notification Channel Creation
test_notification_channel() {
    log "=== Test 5: Notification Channel Creation ==="
    
    # Clear app data
    log "Clearing app data for fresh test..."
    adb shell pm clear "$PACKAGE_NAME"
    sleep 2
    
    # Clear logcat
    adb logcat -c
    
    # Start service
    adb shell am start-service "$SERVICE_NAME"
    sleep 3
    
    # Monitor channel creation
    monitor_logs "(UnifiedBatteryService_Lifecycle|notification.*channel.*created)" 5 "notification channel creation"
    
    # Check if channel exists
    if adb shell dumpsys notification | grep -q "unified_battery_monitor_channel"; then
        log_success "Notification channel created successfully"
    else
        log_warning "Notification channel not found in system"
    fi
}

# Performance validation
validate_performance() {
    log "=== Performance Validation ==="
    
    # Start app
    adb shell am start -n "$MAIN_ACTIVITY"
    sleep 5
    
    # Get initial memory usage
    local initial_memory=$(adb shell dumpsys meminfo "$PACKAGE_NAME" | grep "TOTAL" | awk '{print $2}')
    log "Initial memory usage: ${initial_memory}KB"
    
    # Restart service multiple times
    for i in {1..3}; do
        log "Service restart cycle $i/3"
        adb shell am force-stop "$PACKAGE_NAME"
        sleep 2
        adb shell am start-service "$SERVICE_NAME"
        sleep 5
    done
    
    # Get final memory usage
    local final_memory=$(adb shell dumpsys meminfo "$PACKAGE_NAME" | grep "TOTAL" | awk '{print $2}')
    log "Final memory usage: ${final_memory}KB"
    
    # Check for significant memory increase (potential leak)
    if [ "$final_memory" -gt $((initial_memory + 10000)) ]; then
        log_warning "Significant memory increase detected (${initial_memory}KB -> ${final_memory}KB)"
    else
        log_success "Memory usage stable"
    fi
}

# Generate test report
generate_report() {
    log "=== Test Report ==="
    log "Test completed at $(date)"
    log "Log file: $LOG_FILE"
    
    # Count errors and warnings
    local error_count=$(grep -c "\[ERROR\]" "$LOG_FILE" || echo "0")
    local warning_count=$(grep -c "\[WARNING\]" "$LOG_FILE" || echo "0")
    
    log "Errors: $error_count"
    log "Warnings: $warning_count"
    
    if [ "$error_count" -eq 0 ]; then
        log_success "All tests completed without errors"
    else
        log_error "$error_count errors detected. Please review the log file."
    fi
}

# Main execution
main() {
    log "Starting Foreground Service Fix Testing"
    log "Package: $PACKAGE_NAME"
    log "Log file: $LOG_FILE"
    
    check_device
    install_app
    
    test_background_startup
    test_foreground_startup
    test_service_restart
    test_battery_monitoring
    test_notification_channel
    validate_performance
    
    generate_report
}

# Run main function
main "$@"
