# Foreground Service Fix - Comprehensive ADB Testing Guide

## Overview
This document provides comprehensive testing procedures for the UnifiedBatteryNotificationService foreground service crash fix, specifically targeting Android 12+ background service restrictions.

## Test Environment Setup

### Prerequisites
- Android 12+ device or emulator
- ADB installed and configured
- Bundle ID: `com.fc.p.tj.charginganimation.batterycharging.chargeeffect`

### Emulator Setup
```bash
# Create Android 12 emulator
avd create -n "Android12_Test" -k "system-images;android-31;google_apis;x86_64"
emulator -avd Android12_Test

# Verify emulator is running
adb devices
```

## Testing Scenarios

### 1. Background App Startup Test
Tests the fix for ForegroundServiceStartNotAllowedException when app starts from background.

```bash
# Install and prepare app
adb install -r app/build/outputs/apk/debug/app-debug.apk

# Force app to background and kill
adb shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect

# Start app from background context (simulates notification tap, etc.)
adb shell am start-service com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.features.stats.notifications.UnifiedBatteryNotificationService

# Monitor logs for error handling
adb logcat | grep -E "(UnifiedBatteryService|ServiceManager|ERROR|ForegroundServiceStartNotAllowedException)"
```

**Expected Results:**
- No crashes or ForegroundServiceStartNotAllowedException
- Fallback mode activation logged
- Service continues running in background mode
- Core battery monitoring functionality preserved

### 2. Foreground Context Service Startup Test
Tests normal service startup when app is in foreground.

```bash
# Start app normally
adb shell am start -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity

# Wait for app to fully load (5 seconds)
sleep 5

# Monitor service startup logs
adb logcat | grep -E "(ServiceManager_Foreground|UnifiedBatteryService_Foreground|UnifiedBatteryService_Lifecycle)"
```

**Expected Results:**
- Foreground service starts successfully
- No fallback mode activation
- All services running normally
- Proper notification channel creation

### 3. Service Restart Scenarios
Tests service restart behavior in different contexts.

```bash
# Start app and let services initialize
adb shell am start -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity
sleep 5

# Kill the service
adb shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect

# Put app in background
adb shell input keyevent KEYCODE_HOME

# Try to restart service (should trigger background restrictions)
adb shell am start-service com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.features.stats.notifications.UnifiedBatteryNotificationService

# Monitor restart behavior
adb logcat | grep -E "(ServiceManager|UnifiedBatteryService_Fallback|ERROR)"
```

**Expected Results:**
- Service restart handled gracefully
- Fallback mechanisms activated
- No app crashes
- Battery monitoring continues

### 4. Battery State Monitoring Test
Verifies that core battery monitoring continues even when foreground service fails.

```bash
# Start app in background mode
adb shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect
adb shell am start-service com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.features.stats.notifications.UnifiedBatteryNotificationService

# Simulate battery state changes
adb shell dumpsys battery set ac 1  # Start charging
sleep 10
adb shell dumpsys battery set ac 0  # Stop charging
sleep 10

# Monitor battery monitoring logs
adb logcat | grep -E "(UnifiedBatteryService_Monitoring|CoreBatteryStatsService|BatteryHistoryManager)"
```

**Expected Results:**
- Battery state changes detected and logged
- Alarm conditions checked properly
- Anti-theft logic functioning
- No monitoring interruptions

### 5. Notification Channel Creation Test
Verifies notification channel is created properly.

```bash
# Clear app data to test fresh installation
adb shell pm clear com.fc.p.tj.charginganimation.batterycharging.chargeeffect

# Start service
adb shell am start-service com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.features.stats.notifications.UnifiedBatteryNotificationService

# Check notification channels
adb shell dumpsys notification | grep -A 10 "unified_battery_monitor_channel"

# Monitor channel creation logs
adb logcat | grep -E "(UnifiedBatteryService_Lifecycle|notification.*channel.*created)"
```

**Expected Results:**
- Notification channel created successfully
- Channel properties set correctly (LOW importance, no badge, etc.)
- No channel creation errors

## Monitoring Commands

### Real-time Log Monitoring
```bash
# Comprehensive service monitoring
adb logcat | grep -E "(UnifiedBatteryService|ServiceManager|CoreBatteryStatsService|ERROR|ForegroundServiceStartNotAllowedException)"

# Foreground service specific monitoring
adb logcat | grep -E "(UnifiedBatteryService_Foreground|UnifiedBatteryService_Fallback|ServiceManager_Foreground)"

# Error and exception monitoring
adb logcat | grep -E "(ERROR|Exception|FATAL|UnifiedBatteryService_Error)"
```

### Service Status Verification
```bash
# Check running services
adb shell dumpsys activity services | grep -A 5 "UnifiedBatteryNotificationService"

# Check app process status
adb shell ps | grep com.fc.p.tj.charginganimation.batterycharging.chargeeffect

# Check notification status
adb shell dumpsys notification | grep -A 20 com.fc.p.tj.charginganimation.batterycharging.chargeeffect
```

## Performance Validation

### Memory Usage Monitoring
```bash
# Monitor memory usage during service operation
adb shell dumpsys meminfo com.fc.p.tj.charginganimation.batterycharging.chargeeffect

# Check for memory leaks during service restarts
adb shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect
adb shell am start-service com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.features.stats.notifications.UnifiedBatteryNotificationService
sleep 30
adb shell dumpsys meminfo com.fc.p.tj.charginganimation.batterycharging.chargeeffect
```

### Battery Usage Monitoring
```bash
# Check battery usage impact
adb shell dumpsys batterystats | grep -A 10 com.fc.p.tj.charginganimation.batterycharging.chargeeffect
```

## Test Results Documentation

### Success Criteria
- [ ] No ForegroundServiceStartNotAllowedException crashes
- [ ] Fallback mode activates properly in background contexts
- [ ] Foreground service starts normally when app is in foreground
- [ ] Core battery monitoring continues in all scenarios
- [ ] Notification channels created successfully
- [ ] Service restarts handled gracefully
- [ ] Memory usage remains stable
- [ ] No legacy service conflicts

### Failure Investigation
If tests fail, check:
1. Android version compatibility (API 31+)
2. App permissions and battery optimization settings
3. Service manifest declarations
4. Notification channel creation timing
5. AppLifecycleManager state accuracy

## Automated Testing Script
See `scripts/test_foreground_service_fix.sh` for automated test execution.
